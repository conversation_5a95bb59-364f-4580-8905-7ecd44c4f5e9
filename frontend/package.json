{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.3", "@lucide/svelte": "^0.483.0", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "autoprefixer": "^10.4.20", "bits-ui": "^1.3.14", "clsx": "^2.1.1", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "lucide-svelte": "^0.469.0", "mode-watcher": "^0.5.1", "paneforge": "^1.0.0-next.4", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^0.3.28", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^5.4.11"}, "dependencies": {"@monaco-editor/loader": "^1.3.3", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tanstack/table-core": "^8.21.2", "monaco-editor": "^0.52.2"}}