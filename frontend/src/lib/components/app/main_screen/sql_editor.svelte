<script lang="ts">
	import loader from '@monaco-editor/loader';
	import { onMount, onDestroy } from 'svelte';

	let editorContainer: HTMLElement;
	let editor: import('monaco-editor').editor.IStandaloneCodeEditor;
	let completionProviderDisposable: import('monaco-editor').IDisposable;
	let isInitialized = false;
	let monacoInstance: typeof import('monaco-editor');

	let {
		value = $bindable(),
		selectedText = $bindable(),
		height = '100%',
		width = '100%',
		suggestions = []
	} = $props();

	// Interface for query blocks
	interface QueryBlock {
		query: string;
		startLine: number;
		endLine: number;
		startColumn: number;
		endColumn: number;
	}

	let queryBlocks: QueryBlock[] = [];

	// Function to parse SQL content into query blocks separated by empty lines
	function parseQueryBlocks(content: string): QueryBlock[] {
		const lines = content.split('\n');
		const blocks: QueryBlock[] = [];
		let currentQuery = '';
		let startLine = 1;
		let currentLine = 1;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i].trim();

			if (line === '') {
				// Empty line - end current query block if we have content
				if (currentQuery.trim()) {
					blocks.push({
						query: currentQuery.trim(),
						startLine: startLine,
						endLine: currentLine - 1,
						startColumn: 1,
						endColumn: lines[currentLine - 2]?.length + 1 || 1
					});
					currentQuery = '';
				}
				// Skip empty lines and update start position for next query
				while (i + 1 < lines.length && lines[i + 1].trim() === '') {
					i++;
					currentLine++;
				}
				startLine = currentLine + 1;
			} else {
				// Add line to current query
				if (currentQuery === '') {
					startLine = currentLine;
				}
				currentQuery += (currentQuery ? '\n' : '') + lines[i];
			}
			currentLine++;
		}

		// Add the last query block if it exists
		if (currentQuery.trim()) {
			blocks.push({
				query: currentQuery.trim(),
				startLine: startLine,
				endLine: currentLine - 1,
				startColumn: 1,
				endColumn: lines[currentLine - 2]?.length + 1 || 1
			});
		}

		return blocks;
	}

	// Function to handle cursor position changes and select query block
	function handleCursorPositionChange() {
		if (!editor || !isInitialized) return;

		const position = editor.getPosition();
		if (position) {
			const currentLine = position.lineNumber;

			// Parse current content to get query blocks
			const content = editor.getValue();
			queryBlocks = parseQueryBlocks(content);

			// Find which query block contains the cursor
			const currentBlock = queryBlocks.find(
				(block) => currentLine >= block.startLine && currentLine <= block.endLine
			);

			if (currentBlock) {
				selectedText = currentBlock.query;
			}
		}
	}

	// This effect will run whenever the value changes from outside
	$effect(() => {
		// Only update the editor if it exists and is initialized
		if (editor && isInitialized) {
			// Prevent infinite loops by checking if the editor value is different from the prop value
			const currentValue = editor.getValue();
			if (currentValue !== value) {
				// Update the editor model with the new value
				editor.setValue(value);
			}
		}
	});

	onMount(async () => {
		const monaco = await loader.init();
		monacoInstance = monaco;

		monaco.languages.register({ id: 'sql' });

		completionProviderDisposable = monaco.languages.registerCompletionItemProvider('sql', {
			provideCompletionItems: (model, position) => {
				const word = model.getWordAtPosition(position);
				if (!word) {
					return { suggestions: [] };
				}

				const prefix = word.word.toLowerCase();
				const filteredSuggestions = suggestions.filter((keyword) =>
					keyword.toLowerCase().startsWith(prefix)
				);

				return {
					suggestions: filteredSuggestions.map((keyword) => ({
						label: keyword,
						kind: monaco.languages.CompletionItemKind.Keyword,
						insertText: keyword,
						range: {
							startLineNumber: position.lineNumber,
							startColumn: position.column - prefix.length,
							endLineNumber: position.lineNumber,
							endColumn: position.column
						}
					}))
				};
			}
		});

		editor = monaco.editor.create(editorContainer, {
			value: value,
			language: 'sql',
			theme: 'vs-dark',
			minimap: { enabled: false },
			fontSize: 14,
			automaticLayout: true,
			glyphMargin: true
		});

		// Update value when editor content changes
		editor.onDidChangeModelContent(() => {
			value = editor.getValue();
		});

		// Handle cursor position changes
		editor.onDidChangeCursorPosition(() => {
			handleCursorPositionChange();
		});

		// Handle mouse clicks
		editor.onMouseDown((e) => {
			if (e.target.position) {
				handleCursorPositionChange();
			}
		});

		// Manually trigger suggestions on key press
		editor.onKeyDown((e) => {
			if (e.keyCode === 32 || e.keyCode === 9) {
				// Space or Tab
				// Use the correct command for triggering suggestions
				editor.trigger('keyboard', 'editor.action.triggerSuggest', null);
			}
		});

		editor.createDecorationsCollection([
			{
				range: new monaco.Range(1, 1, 1, 1),
				options: {
					isWholeLine: true,
					className: 'bg-green-100 bg-opacity-5',
					glyphMarginClassName: 'bg-green-500 bg-opacity-20',
					glyphMarginHoverMessage: { value: 'Run Query' }
				}
			},
			{
				range: new monaco.Range(3, 1, 6, 1),
				options: {
					isWholeLine: true,
					className: 'bg-green-100 bg-opacity-5',
					glyphMarginClassName: 'bg-green-500 bg-opacity-20',
					glyphMarginHoverMessage: { value: 'Run Query' }
				}
			},
			{
				range: new monaco.Range(8, 1, 11, 1),
				options: {
					isWholeLine: true,
					className: 'bg-green-100 bg-opacity-5',
					glyphMarginClassName: 'bg-green-500 bg-opacity-20',
					glyphMarginHoverMessage: { value: 'Run Query' }
				}
			}
		]);

		// Mark as initialized after editor is created
		isInitialized = true;
	});

	onDestroy(() => {
		editor?.dispose();
		completionProviderDisposable?.dispose();
	});

	// Function to update the state with the selected text
	function handleSelection() {
		const selection = window.getSelection();
		if (selection) {
			selectedText = selection.toString();
		}
	}
</script>

<div
	onselect={handleSelection}
	bind:this={editorContainer}
	class="sql-editor"
	style="height: {height}; width: {width};"
></div>
