<script lang="ts">
	import loader from '@monaco-editor/loader';
	import { onMount, onDestroy } from 'svelte';

	let editorContainer: HTMLElement;
	let editor: import('monaco-editor').editor.IStandaloneCodeEditor;
	let completionProviderDisposable: import('monaco-editor').IDisposable;
	let isInitialized = false;
	let decorations: string[] = [];

	let {
		value = $bindable(),
		selectedText = $bindable(),
		height = '100%',
		width = '100%',
		suggestions = []
	} = $props();

	// Interface for query blocks
	interface QueryBlock {
		query: string;
		startLine: number;
		endLine: number;
		startColumn: number;
		endColumn: number;
	}

	let queryBlocks: QueryBlock[] = [];

	// Function to parse SQL content into query blocks separated by empty lines
	function parseQueryBlocks(content: string): QueryBlock[] {
		const lines = content.split('\n');
		const blocks: QueryBlock[] = [];
		let currentQuery = '';
		let startLine = 1;
		let currentLine = 1;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i].trim();

			if (line === '') {
				// Empty line - end current query block if we have content
				if (currentQuery.trim()) {
					blocks.push({
						query: currentQuery.trim(),
						startLine: startLine,
						endLine: currentLine - 1,
						startColumn: 1,
						endColumn: lines[currentLine - 2]?.length + 1 || 1
					});
					currentQuery = '';
				}
				// Skip empty lines and update start position for next query
				while (i + 1 < lines.length && lines[i + 1].trim() === '') {
					i++;
					currentLine++;
				}
				startLine = currentLine + 1;
			} else {
				// Add line to current query
				if (currentQuery === '') {
					startLine = currentLine;
				}
				currentQuery += (currentQuery ? '\n' : '') + lines[i];
			}
			currentLine++;
		}

		// Add the last query block if it exists
		if (currentQuery.trim()) {
			blocks.push({
				query: currentQuery.trim(),
				startLine: startLine,
				endLine: currentLine - 1,
				startColumn: 1,
				endColumn: lines[currentLine - 2]?.length + 1 || 1
			});
		}

		return blocks;
	}

	// Function to update decorations for query blocks
	function updateQueryDecorations() {
		if (!editor || !isInitialized) return;

		const content = editor.getValue();
		queryBlocks = parseQueryBlocks(content);

		// Clear existing decorations
		decorations = editor.deltaDecorations(decorations, []);

		// Add new decorations for each query block
		const newDecorations = queryBlocks.map((block) => ({
			range: {
				startLineNumber: block.startLine,
				startColumn: 1,
				endLineNumber: block.endLine,
				endColumn: Number.MAX_SAFE_INTEGER
			},
			options: {
				className: 'query-block-highlight',
				hoverMessage: { value: 'Click to execute this query' },
				stickiness: 1 // monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
			}
		}));

		decorations = editor.deltaDecorations([], newDecorations);
	}

	// This effect will run whenever the value changes from outside
	$effect(() => {
		const _ = value;

		// Only update the editor if it exists and is initialized
		if (editor && isInitialized) {
			// Prevent infinite loops by checking if the editor value is different from the prop value
			const currentValue = editor.getValue();
			if (currentValue !== value) {
				// Update the editor model with the new value
				editor.setValue(value);
			}
			// Update query block decorations when content changes
			updateQueryDecorations();
		}
	});

	onMount(async () => {
		const monaco = await loader.init();

		monaco.languages.register({ id: 'sql' });

		completionProviderDisposable = monaco.languages.registerCompletionItemProvider('sql', {
			provideCompletionItems: (model, position) => {
				const word = model.getWordAtPosition(position);
				if (!word) {
					return { suggestions: [] };
				}

				const prefix = word.word.toLowerCase();
				const filteredSuggestions = suggestions.filter((keyword) =>
					keyword.toLowerCase().startsWith(prefix)
				);

				return {
					suggestions: filteredSuggestions.map((keyword) => ({
						label: keyword,
						kind: monaco.languages.CompletionItemKind.Keyword,
						insertText: keyword,
						range: {
							startLineNumber: position.lineNumber,
							startColumn: position.column - prefix.length,
							endLineNumber: position.lineNumber,
							endColumn: position.column
						}
					}))
				};
			}
		});

		editor = monaco.editor.create(editorContainer, {
			value: value,
			language: 'sql',
			theme: 'vs-dark',
			minimap: { enabled: false },
			fontSize: 14,
			automaticLayout: true
		});

		// Update value when editor content changes
		editor.onDidChangeModelContent(() => {
			value = editor.getValue();
			// Update decorations when content changes
			updateQueryDecorations();
		});

		// Handle mouse clicks to detect query block selection
		editor.onMouseDown((e) => {
			if (e.target.position) {
				const clickedLine = e.target.position.lineNumber;

				// Find which query block was clicked
				const clickedBlock = queryBlocks.find(
					(block) => clickedLine >= block.startLine && clickedLine <= block.endLine
				);

				if (clickedBlock) {
					selectedText = clickedBlock.query;
				}
			}
		});

		// Manually trigger suggestions on key press
		editor.onKeyDown((e) => {
			if (e.keyCode === 32 || e.keyCode === 9) {
				// Space or Tab
				// Use the correct command for triggering suggestions
				editor.trigger('keyboard', 'editor.action.triggerSuggest', null);
			}
		});

		// Mark as initialized after editor is created
		isInitialized = true;

		// Initial decoration update
		updateQueryDecorations();
	});

	onDestroy(() => {
		editor?.dispose();
		completionProviderDisposable?.dispose();
	});

	// Function to update the state with the selected text
	function handleSelection() {
		const selection = window.getSelection();
		if (selection) {
			selectedText = selection.toString();
		}
	}
</script>

<div
	onselect={handleSelection}
	bind:this={editorContainer}
	class="sql-editor"
	style="height: {height}; width: {width};"
></div>

<style>
	:global(.query-block-highlight) {
		background-color: rgba(0, 122, 255, 0.1);
		border-left: 3px solid rgba(0, 122, 255, 0.3);
		padding-left: 4px;
		margin-left: -4px;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	:global(.query-block-highlight:hover) {
		background-color: rgba(0, 122, 255, 0.15);
		border-left-color: rgba(0, 122, 255, 0.5);
	}

	:global(.monaco-editor .query-block-highlight) {
		border-radius: 3px;
	}
</style>
