// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddPostgresConnection(arg1) {
  return window['go']['app']['Connections']['AddPostgresConnection'](arg1);
}

export function EstablishPostgresConnection(arg1) {
  return window['go']['app']['Connections']['EstablishPostgresConnection'](arg1);
}

export function EstablishPostgresDatabaseConnection(arg1, arg2, arg3) {
  return window['go']['app']['Connections']['EstablishPostgresDatabaseConnection'](arg1, arg2, arg3);
}

export function ExecuteQuery(arg1, arg2, arg3) {
  return window['go']['app']['Connections']['ExecuteQuery'](arg1, arg2, arg3);
}

export function GetAllDatabaseColumns(arg1) {
  return window['go']['app']['Connections']['GetAllDatabaseColumns'](arg1);
}

export function GetAllPostgresTables(arg1) {
  return window['go']['app']['Connections']['GetAllPostgresTables'](arg1);
}

export function GetPostgresConnections() {
  return window['go']['app']['Connections']['GetPostgresConnections']();
}

export function GetPostgresServerDatabases(arg1, arg2, arg3, arg4, arg5) {
  return window['go']['app']['Connections']['GetPostgresServerDatabases'](arg1, arg2, arg3, arg4, arg5);
}

export function GetSqlite3Version() {
  return window['go']['app']['Connections']['GetSqlite3Version']();
}

export function RefreshPostgresDatabase(arg1, arg2, arg3, arg4) {
  return window['go']['app']['Connections']['RefreshPostgresDatabase'](arg1, arg2, arg3, arg4);
}

export function TerminatePostgresDatabaseConnection(arg1) {
  return window['go']['app']['Connections']['TerminatePostgresDatabaseConnection'](arg1);
}

export function TestConnectPostgres(arg1) {
  return window['go']['app']['Connections']['TestConnectPostgres'](arg1);
}

export function UpdateTabOutput(arg1, arg2) {
  return window['go']['app']['Connections']['UpdateTabOutput'](arg1, arg2);
}
