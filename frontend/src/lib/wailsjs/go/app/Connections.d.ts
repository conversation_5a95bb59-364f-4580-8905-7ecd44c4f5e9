// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {model} from '../models';
import {uuid} from '../models';

export function AddPostgresConnection(arg1:model.PostgresConnection):Promise<boolean>;

export function EstablishPostgresConnection(arg1:number):Promise<Array<model.Database>>;

export function EstablishPostgresDatabaseConnection(arg1:number,arg2:string,arg3:string):Promise<model.Database>;

export function ExecuteQuery(arg1:uuid.UUID,arg2:string,arg3:number):Promise<model.QueryResult>;

export function GetAllDatabaseColumns(arg1:uuid.UUID):Promise<Array<string>>;

export function GetAllPostgresTables(arg1:uuid.UUID):Promise<Array<string>>;

export function GetPostgresConnections():Promise<Array<model.PostgresConnection>>;

export function GetPostgresServerDatabases(arg1:number,arg2:uuid.UUID,arg3:string,arg4:string,arg5:string):Promise<Array<model.Database>>;

export function GetSqlite3Version():Promise<string>;

export function RefreshPostgresDatabase(arg1:number,arg2:string,arg3:string,arg4:string):Promise<model.Database>;

export function TerminatePostgresDatabaseConnection(arg1:string):Promise<boolean>;

export function TestConnectPostgres(arg1:model.PostgresConnection):Promise<boolean>;

export function UpdateTabOutput(arg1:number,arg2:model.Output):Promise<void>;
